# macOS风格记事本应用

一个使用PyQt6开发的现代化记事本应用，具有macOS风格的用户界面设计。

## 功能特性

### 已实现的UI框架
- ✅ **macOS风格界面设计** - 采用现代化的macOS设计语言
  - 🎨 **圆角边框** - 所有组件都采用柔和的圆角设计
  - 🌟 **无边框窗口** - 自定义标题栏，完全的macOS风格
  - 🎯 **macOS窗口控制** - 红绿黄三色窗口控制按钮
  - ✨ **阴影效果** - 窗口投影，增强立体感
  - 🎨 **半透明效果** - 使用rgba颜色，营造层次感
- ✅ **三栏布局** - 左侧分类栏、中间记事列表、右侧编辑器
- ✅ **响应式布局** - 支持窗口大小调整，面板可拖拽调整
- ✅ **工具栏** - 包含新建记事、新建分类、搜索功能
- ✅ **SQLite数据库** - 本地数据存储，支持分类和记事管理
- ✅ **搜索功能** - 实时搜索记事标题和内容
- ✅ **交互体验** - 悬停效果、焦点状态、工具提示
- ✅ **自定义滚动条** - macOS风格的细滚动条
- ✅ **窗口拖拽** - 可通过标题栏拖拽移动窗口

### 待实现的功能
- 🔄 **记事管理** - 添加、编辑、删除记事
- 🔄 **分类管理** - 创建、编辑、删除分类
- 🔄 **自动保存** - 实时保存编辑内容
- 🔄 **右键菜单** - 分类和记事的上下文菜单
- 🔄 **导入导出** - 支持多种格式的导入导出
- 🔄 **主题切换** - 支持浅色/深色主题

## 技术栈

- **GUI框架**: PyQt6
- **数据库**: SQLite3
- **编程语言**: Python 3.8+
- **打包工具**: PyInstaller

## 安装和运行

### 环境要求
- Python 3.8 或更高版本
- PyQt6

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python main.py
```

## 项目结构

```
.
├── main.py              # 主应用程序文件
├── requirements.txt     # 项目依赖
├── README.md           # 项目说明文档
└── notes.db            # SQLite数据库文件（运行后自动创建）
```

## 界面预览

### macOS风格设计特色
- **无边框窗口** - 自定义标题栏，完全模拟macOS应用
- **圆角设计** - 所有组件都采用8-12px的圆角，柔和美观
- **阴影效果** - 窗口具有自然的投影，增强立体感
- **半透明背景** - 使用rgba颜色，营造层次丰富的视觉效果
- **macOS窗口控制** - 红绿黄三色按钮，支持关闭、最小化、最大化
- **细节优化** - 悬停效果、焦点状态、自定义滚动条

### 应用程序布局

应用程序采用三栏布局：

1. **自定义标题栏**
   - macOS风格的红绿黄控制按钮
   - 居中显示应用标题
   - 支持拖拽移动窗口

2. **工具栏**
   - 新建记事按钮（蓝色主按钮）
   - 新建分类按钮（灰色次要按钮）
   - 搜索框（带占位符和工具提示）

3. **左侧栏** - 分类管理
   - 显示所有分类
   - 圆角背景，半透明效果
   - 支持点击切换分类
   - 悬停高亮效果

4. **中间栏** - 记事列表
   - 显示当前分类下的所有记事
   - 显示记事数量统计
   - 支持点击选择记事
   - 圆角列表项，选中高亮

5. **右侧栏** - 编辑器
   - 记事标题编辑（大字体，无边框）
   - 记事内容编辑（圆角文本框）
   - 显示创建/修改时间和分类信息
   - 红色删除按钮（圆角设计）

## 数据库设计

### 分类表 (categories)
- `id` - 主键
- `name` - 分类名称
- `color` - 分类颜色
- `created_at` - 创建时间

### 记事表 (notes)
- `id` - 主键
- `title` - 记事标题
- `content` - 记事内容
- `category_id` - 所属分类ID
- `created_at` - 创建时间
- `updated_at` - 更新时间

## 开发计划

### 第一阶段 ✅
- [x] 基础UI框架搭建
- [x] 数据库设计和初始化
- [x] 基本的数据加载和显示

### 第二阶段 🔄
- [ ] 实现记事的增删改功能
- [ ] 实现分类的增删改功能
- [ ] 添加自动保存功能

### 第三阶段 📋
- [ ] 添加搜索和过滤功能
- [ ] 实现右键菜单
- [ ] 添加快捷键支持

### 第四阶段 📋
- [ ] 主题切换功能
- [ ] 导入导出功能
- [ ] 打包为exe可执行文件

## 打包部署

使用PyInstaller将应用打包为exe文件：

```bash
pyinstaller --onefile --windowed main.py
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
