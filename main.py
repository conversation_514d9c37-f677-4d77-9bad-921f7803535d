"""
macOS风格记事本应用 - PyQt6版本
主要功能：
- 分类管理（创建、编辑、删除分类）
- 记事管理（添加、编辑、删除记事）
- SQLite数据存储
- macOS风格UI设计
"""

import sys
import sqlite3
import datetime
from typing import Optional, List, Dict

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QSplitter, QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
    QTextEdit, QLineEdit, QPushButton, QLabel, QFrame,
    QMessageBox, QInputDialog, QMenu, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QAction, QPalette, QColor

class NotesApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_note_id = None
        self.current_category_id = None
        self.notes_data = []

        self.setup_database()
        self.setup_ui()
        self.setup_styles()
        self.load_data()

    def setup_database(self):
        """初始化数据库"""
        self.db_path = "notes.db"
        self.conn = sqlite3.connect(self.db_path)
        self.create_tables()

    def create_tables(self):
        """创建数据库表"""
        cursor = self.conn.cursor()

        # 创建分类表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                color TEXT DEFAULT '#007AFF',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建记事表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # 插入默认分类
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('所有记事',))
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('工作',))
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('个人',))

        self.conn.commit()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Notes - macOS Style")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)

        # 创建主容器，用于实现圆角效果
        main_container = QWidget()
        main_container.setObjectName("mainContainer")

        # 设置主容器样式
        main_container.setStyleSheet("""
            QWidget#mainContainer {
                background-color: #f5f5f5;
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)

        self.setCentralWidget(main_container)

        # 创建主布局
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(8)

        # 创建自定义标题栏
        self.create_title_bar(main_layout)

        # 创建工具栏
        self.create_toolbar(main_layout)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet("""
            QSplitter {
                border-radius: 8px;
                background-color: transparent;
            }
        """)
        main_layout.addWidget(splitter)

        # 创建侧边栏（分类列表）
        self.create_sidebar(splitter)

        # 创建记事列表
        self.create_notes_list(splitter)

        # 创建编辑器
        self.create_editor(splitter)

        # 设置分割器比例
        splitter.setSizes([200, 300, 500])

    def create_title_bar(self, layout):
        """创建自定义标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(40)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: rgba(245, 245, 245, 0.95);
                border-radius: 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(16, 0, 16, 0)

        # macOS风格的窗口控制按钮
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(8)

        # 关闭按钮
        close_btn = QPushButton()
        close_btn.setFixedSize(12, 12)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5f57;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #ff4136;
            }
        """)
        close_btn.clicked.connect(self.close)

        # 最小化按钮
        minimize_btn = QPushButton()
        minimize_btn.setFixedSize(12, 12)
        minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffbd2e;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #ffaa00;
            }
        """)
        minimize_btn.clicked.connect(self.showMinimized)

        # 最大化按钮
        maximize_btn = QPushButton()
        maximize_btn.setFixedSize(12, 12)
        maximize_btn.setStyleSheet("""
            QPushButton {
                background-color: #28ca42;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #20a034;
            }
        """)
        maximize_btn.clicked.connect(self.toggle_maximize)

        controls_layout.addWidget(close_btn)
        controls_layout.addWidget(minimize_btn)
        controls_layout.addWidget(maximize_btn)

        # 标题
        title_label = QLabel("Notes")
        title_label.setStyleSheet("""
            QLabel {
                color: #1d1d1f;
                font-size: 14px;
                font-weight: 600;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """)

        title_layout.addWidget(controls_widget)
        title_layout.addStretch()
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addWidget(title_bar)

        # 使标题栏可拖拽
        title_bar.mousePressEvent = self.mouse_press_event
        title_bar.mouseMoveEvent = self.mouse_move_event

    def add_shadow_effect(self):
        """添加阴影效果"""
        from PyQt6.QtWidgets import QGraphicsDropShadowEffect
        from PyQt6.QtGui import QColor

        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)

    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()

    def mouse_press_event(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()

    def mouse_move_event(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)

    def setup_styles(self):
        """设置样式"""
        # macOS风格样式表 - 增强圆角和柔和美感
        style = """
            QMainWindow {
                background-color: #f5f5f5;
                border-radius: 12px;
            }

            QToolBar {
                background-color: rgba(245, 245, 245, 0.95);
                border: none;
                border-radius: 8px;
                padding: 12px;
                margin: 8px;
            }

            QPushButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 16px;
                font-size: 11px;
                font-weight: 500;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            QPushButton:hover {
                background-color: #0056CC;
                transform: translateY(-1px);
            }

            QPushButton:pressed {
                background-color: #004499;
                transform: translateY(0px);
            }

            QPushButton#secondary {
                background-color: #e5e5e7;
                color: #1d1d1f;
                border: 1px solid #d2d2d7;
            }

            QPushButton#secondary:hover {
                background-color: #d1d1d6;
                border: 1px solid #c7c7cc;
            }

            QLineEdit {
                background-color: rgba(255, 255, 255, 0.9);
                border: 1px solid #d2d2d7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 11px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            QLineEdit:focus {
                border: 2px solid #007AFF;
                background-color: white;
            }

            QTreeWidget {
                background-color: rgba(232, 232, 232, 0.8);
                border: none;
                border-radius: 10px;
                font-size: 13px;
                outline: none;
                padding: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            QTreeWidget::item {
                padding: 10px 12px;
                border: none;
                border-radius: 6px;
                margin: 2px 0px;
            }

            QTreeWidget::item:hover {
                background-color: rgba(0, 122, 255, 0.1);
            }

            QTreeWidget::item:selected {
                background-color: #007AFF;
                color: white;
                border-radius: 6px;
            }

            QListWidget {
                background-color: rgba(255, 255, 255, 0.95);
                border: none;
                border-radius: 10px;
                font-size: 12px;
                outline: none;
                padding: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            QListWidget::item {
                padding: 12px 16px;
                border: none;
                border-radius: 8px;
                margin: 2px 0px;
                border-bottom: 1px solid rgba(240, 240, 240, 0.5);
            }

            QListWidget::item:hover {
                background-color: rgba(0, 122, 255, 0.05);
            }

            QListWidget::item:selected {
                background-color: #007AFF;
                color: white;
                border-radius: 8px;
                border-bottom: none;
            }

            QTextEdit {
                background-color: rgba(255, 255, 255, 0.95);
                border: none;
                border-radius: 10px;
                font-size: 12px;
                padding: 16px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.5;
            }

            QTextEdit:focus {
                background-color: white;
            }

            QLabel {
                color: #1d1d1f;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            QLabel#secondary {
                color: #86868b;
                font-size: 11px;
            }

            QFrame#separator {
                background-color: rgba(209, 209, 214, 0.6);
                max-height: 1px;
                min-height: 1px;
                border-radius: 0.5px;
            }

            QWidget {
                border-radius: 8px;
            }

            QSplitter::handle {
                background-color: rgba(209, 209, 214, 0.3);
                border-radius: 2px;
            }

            QSplitter::handle:horizontal {
                width: 1px;
                margin: 8px 0px;
            }

            QSplitter::handle:vertical {
                height: 1px;
                margin: 0px 8px;
            }

            /* 滚动条样式 */
            QScrollBar:vertical {
                background-color: rgba(0, 0, 0, 0.05);
                width: 8px;
                border-radius: 4px;
                margin: 0px;
            }

            QScrollBar::handle:vertical {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: rgba(0, 0, 0, 0.3);
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }

            QScrollBar:horizontal {
                background-color: rgba(0, 0, 0, 0.05);
                height: 8px;
                border-radius: 4px;
                margin: 0px;
            }

            QScrollBar::handle:horizontal {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
                min-width: 20px;
                margin: 2px;
            }

            QScrollBar::handle:horizontal:hover {
                background-color: rgba(0, 0, 0, 0.3);
            }

            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }

            /* 工具提示样式 */
            QToolTip {
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 11px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """

        self.setStyleSheet(style)

    def create_toolbar(self, layout):
        """创建工具栏"""
        toolbar_widget = QWidget()
        toolbar_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(245, 245, 245, 0.95);
                border-radius: 8px;
                padding: 8px;
                margin: 4px;
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 8, 12, 8)
        toolbar_layout.setSpacing(12)

        # 添加记事按钮
        add_note_btn = QPushButton("+ 新建记事")
        add_note_btn.setToolTip("创建一个新的记事 (Cmd+N)")
        add_note_btn.clicked.connect(self.add_note)
        toolbar_layout.addWidget(add_note_btn)

        # 添加分类按钮
        add_category_btn = QPushButton("+ 新建分类")
        add_category_btn.setObjectName("secondary")
        add_category_btn.setToolTip("创建一个新的分类")
        add_category_btn.clicked.connect(self.add_category)
        toolbar_layout.addWidget(add_category_btn)

        # 添加弹性空间
        toolbar_layout.addStretch()

        # 搜索框
        search_label = QLabel("搜索:")
        toolbar_layout.addWidget(search_label)

        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("搜索记事...")
        self.search_entry.setToolTip("搜索记事标题和内容 (Cmd+F)")
        self.search_entry.setMaximumWidth(200)
        self.search_entry.textChanged.connect(self.on_search)
        toolbar_layout.addWidget(self.search_entry)

        layout.addWidget(toolbar_widget)

    def create_sidebar(self, splitter):
        """创建左侧分类侧边栏"""
        # 侧边栏容器
        sidebar_widget = QWidget()
        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(15, 15, 15, 15)

        # 侧边栏标题
        sidebar_title = QLabel("分类")
        sidebar_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;")
        sidebar_layout.addWidget(sidebar_title)

        # 分类列表
        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderHidden(True)
        self.categories_tree.itemClicked.connect(self.on_category_select)
        self.categories_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.categories_tree.customContextMenuRequested.connect(self.show_category_context_menu)
        sidebar_layout.addWidget(self.categories_tree)

        splitter.addWidget(sidebar_widget)

    def create_notes_list(self, splitter):
        """创建中间记事列表"""
        # 记事列表容器
        notes_widget = QWidget()
        notes_layout = QVBoxLayout(notes_widget)
        notes_layout.setContentsMargins(15, 15, 15, 15)

        # 记事列表标题栏
        notes_header = QWidget()
        notes_header_layout = QHBoxLayout(notes_header)
        notes_header_layout.setContentsMargins(0, 0, 0, 0)

        self.notes_title_label = QLabel("所有记事")
        self.notes_title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;")
        notes_header_layout.addWidget(self.notes_title_label)

        # 记事数量标签
        self.notes_count_label = QLabel("0 条记事")
        self.notes_count_label.setObjectName("secondary")
        notes_header_layout.addWidget(self.notes_count_label, alignment=Qt.AlignmentFlag.AlignRight)

        notes_layout.addWidget(notes_header)

        # 分隔线
        separator = QFrame()
        separator.setObjectName("separator")
        separator.setFrameShape(QFrame.Shape.HLine)
        notes_layout.addWidget(separator)

        # 记事列表
        self.notes_listbox = QListWidget()
        self.notes_listbox.itemClicked.connect(self.on_note_select)
        self.notes_listbox.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.notes_listbox.customContextMenuRequested.connect(self.show_note_context_menu)
        notes_layout.addWidget(self.notes_listbox)

        splitter.addWidget(notes_widget)

    def create_editor(self, splitter):
        """创建右侧编辑器"""
        # 编辑器容器
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        editor_layout.setContentsMargins(20, 20, 20, 20)

        # 编辑器标题栏
        editor_header = QWidget()
        editor_header_layout = QVBoxLayout(editor_header)
        editor_header_layout.setContentsMargins(0, 0, 0, 0)

        # 标题输入框
        self.title_entry = QLineEdit()
        self.title_entry.setPlaceholderText("记事标题...")
        self.title_entry.setStyleSheet("font-size: 16px; font-weight: bold; border: none; background: transparent;")
        self.title_entry.textChanged.connect(self.on_title_change)
        editor_header_layout.addWidget(self.title_entry)

        # 元信息栏
        meta_frame = QWidget()
        meta_layout = QHBoxLayout(meta_frame)
        meta_layout.setContentsMargins(0, 10, 0, 0)

        self.meta_label = QLabel("")
        self.meta_label.setObjectName("secondary")
        meta_layout.addWidget(self.meta_label)

        # 删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setStyleSheet("background-color: #FF3B30; color: white; padding: 5px 10px; font-size: 10px;")
        self.delete_btn.setToolTip("删除当前记事 (Delete)")
        self.delete_btn.clicked.connect(self.delete_note)
        meta_layout.addWidget(self.delete_btn, alignment=Qt.AlignmentFlag.AlignRight)

        editor_header_layout.addWidget(meta_frame)
        editor_layout.addWidget(editor_header)

        # 分隔线
        separator = QFrame()
        separator.setObjectName("separator")
        separator.setFrameShape(QFrame.Shape.HLine)
        editor_layout.addWidget(separator)

        # 内容编辑器
        self.content_text = QTextEdit()
        self.content_text.setPlaceholderText("开始写作...")
        self.content_text.textChanged.connect(self.on_content_change)
        editor_layout.addWidget(self.content_text)

        splitter.addWidget(editor_widget)

        # 初始状态下隐藏编辑器
        self.show_welcome_message()

    def show_welcome_message(self):
        """显示欢迎消息"""
        self.title_entry.setEnabled(False)
        self.content_text.setEnabled(False)
        self.delete_btn.setEnabled(False)

        self.title_entry.setText("")
        self.content_text.setText("选择一个记事开始编辑，或创建新的记事。")
        self.meta_label.setText("")

    def load_data(self):
        """加载数据"""
        self.load_categories()
        self.load_notes()

    def load_categories(self):
        """加载分类列表"""
        # 清空现有项目
        self.categories_tree.clear()

        cursor = self.conn.cursor()
        cursor.execute('SELECT id, name FROM categories ORDER BY name')
        categories = cursor.fetchall()

        for category_id, name in categories:
            item = QTreeWidgetItem([name])
            item.setData(0, Qt.ItemDataRole.UserRole, category_id)
            self.categories_tree.addTopLevelItem(item)

        # 默认选择第一个分类
        if categories:
            self.categories_tree.setCurrentItem(self.categories_tree.topLevelItem(0))
            self.current_category_id = categories[0][0]
        else:
            self.current_category_id = None

    def load_notes(self, category_id=None, search_term=""):
        """加载记事列表"""
        self.notes_listbox.clear()
        self.notes_data = []

        cursor = self.conn.cursor()

        if search_term:
            # 搜索模式
            cursor.execute('''
                SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                FROM notes n
                LEFT JOIN categories c ON n.category_id = c.id
                WHERE n.title LIKE ? OR n.content LIKE ?
                ORDER BY n.updated_at DESC
            ''', (f'%{search_term}%', f'%{search_term}%'))
            self.notes_title_label.setText(f"搜索结果: {search_term}")
        elif category_id:
            # 按分类筛选
            if category_id == 1:  # "所有记事"分类
                cursor.execute('''
                    SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                    FROM notes n
                    LEFT JOIN categories c ON n.category_id = c.id
                    ORDER BY n.updated_at DESC
                ''')
                self.notes_title_label.setText("所有记事")
            else:
                cursor.execute('''
                    SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                    FROM notes n
                    LEFT JOIN categories c ON n.category_id = c.id
                    WHERE n.category_id = ?
                    ORDER BY n.updated_at DESC
                ''', (category_id,))
                # 获取分类名称
                cursor.execute('SELECT name FROM categories WHERE id = ?', (category_id,))
                category_name = cursor.fetchone()
                if category_name:
                    self.notes_title_label.setText(category_name[0])
        else:
            # 默认显示所有记事
            cursor.execute('''
                SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                FROM notes n
                LEFT JOIN categories c ON n.category_id = c.id
                ORDER BY n.updated_at DESC
            ''')
            self.notes_title_label.setText("所有记事")

        notes = cursor.fetchall()

        for note in notes:
            note_id, title, content, created_at, updated_at, category_name = note
            self.notes_data.append({
                'id': note_id,
                'title': title,
                'content': content,
                'created_at': created_at,
                'updated_at': updated_at,
                'category_name': category_name or '未分类'
            })

            # 显示标题，如果标题为空则显示内容的前30个字符
            display_title = title if title.strip() else (content[:30] + "..." if len(content) > 30 else content)
            if not display_title.strip():
                display_title = "无标题"

            item = QListWidgetItem(display_title)
            item.setData(Qt.ItemDataRole.UserRole, note_id)
            self.notes_listbox.addItem(item)

        # 更新记事数量
        count = len(notes)
        self.notes_count_label.setText(f"{count} 条记事")

        # 如果没有选中的记事，显示欢迎消息
        if not notes:
            self.show_welcome_message()

    # 事件处理方法
    def on_category_select(self, item, column=None):
        """分类选择事件"""
        if item:
            category_id = item.data(0, Qt.ItemDataRole.UserRole)
            self.current_category_id = category_id
            self.load_notes(category_id)

    def on_note_select(self, item):
        """记事选择事件"""
        if item:
            note_id = item.data(Qt.ItemDataRole.UserRole)
            # 根据note_id找到对应的记事数据
            for note in self.notes_data:
                if note['id'] == note_id:
                    self.load_note_to_editor(note)
                    break

    def on_search(self):
        """搜索事件"""
        search_term = self.search_entry.text().strip()
        if search_term:
            self.load_notes(search_term=search_term)
        else:
            self.load_notes(self.current_category_id)

    def on_title_change(self):
        """标题变化事件"""
        # 占位符 - 后续实现自动保存
        pass

    def on_content_change(self):
        """内容变化事件"""
        # 占位符 - 后续实现自动保存
        pass

    def load_note_to_editor(self, note):
        """加载记事到编辑器"""
        self.current_note_id = note['id']

        # 启用编辑器
        self.title_entry.setEnabled(True)
        self.content_text.setEnabled(True)
        self.delete_btn.setEnabled(True)

        # 设置内容
        self.title_entry.setText(note['title'])
        self.content_text.setText(note['content'] or "")

        # 设置元信息
        try:
            created_time = datetime.datetime.fromisoformat(note['created_at'].replace('Z', '+00:00'))
            updated_time = datetime.datetime.fromisoformat(note['updated_at'].replace('Z', '+00:00'))
        except:
            # 如果时间格式解析失败，使用简单格式
            created_time = datetime.datetime.now()
            updated_time = datetime.datetime.now()

        meta_text = f"创建于 {created_time.strftime('%Y-%m-%d %H:%M')} | "
        meta_text += f"更新于 {updated_time.strftime('%Y-%m-%d %H:%M')} | "
        meta_text += f"分类: {note['category_name']}"

        self.meta_label.setText(meta_text)

    # 功能方法占位符 (后续实现)
    def add_note(self):
        """添加新记事"""
        QMessageBox.information(self, "提示", "添加记事功能将在下一步实现")

    def add_category(self):
        """添加新分类"""
        QMessageBox.information(self, "提示", "添加分类功能将在下一步实现")

    def delete_note(self):
        """删除记事"""
        QMessageBox.information(self, "提示", "删除记事功能将在下一步实现")

    def show_category_context_menu(self, position):
        """显示分类右键菜单"""
        # 占位符 - 后续实现右键菜单
        pass

    def show_note_context_menu(self, position):
        """显示记事右键菜单"""
        # 占位符 - 后续实现右键菜单
        pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = NotesApp()
    window.show()
    sys.exit(app.exec())