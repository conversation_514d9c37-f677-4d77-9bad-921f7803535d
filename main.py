"""
macOS风格记事本应用
主要功能：
- 分类管理（创建、编辑、删除分类）
- 记事管理（添加、编辑、删除记事）
- SQLite数据存储
- macOS风格UI设计
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import datetime
import os
from typing import Optional, List, Dict

class NotesApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_database()
        self.setup_ui()
        self.load_data()

    def setup_window(self):
        """设置主窗口"""
        self.root.title("Notes - macOS Style")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # macOS风格配色
        self.colors = {
            'bg': '#f5f5f5',           # 主背景色
            'sidebar_bg': '#e8e8e8',   # 侧边栏背景
            'selected': '#007AFF',      # 选中项颜色
            'text': '#333333',          # 主文本颜色
            'secondary_text': '#666666', # 次要文本颜色
            'border': '#d1d1d1',        # 边框颜色
            'white': '#ffffff'          # 白色背景
        }

        self.root.configure(bg=self.colors['bg'])

    def setup_database(self):
        """初始化数据库"""
        self.db_path = "notes.db"
        self.conn = sqlite3.connect(self.db_path)
        self.create_tables()

    def create_tables(self):
        """创建数据库表"""
        cursor = self.conn.cursor()

        # 创建分类表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                color TEXT DEFAULT '#007AFF',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建记事表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # 插入默认分类
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('所有记事',))
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('工作',))
        cursor.execute('INSERT OR IGNORE INTO categories (name) VALUES (?)', ('个人',))

        self.conn.commit()

    def setup_ui(self):
        """设置用户界面"""
        self.setup_styles()
        self.create_main_layout()
        self.create_toolbar()
        self.create_sidebar()
        self.create_notes_list()
        self.create_editor()

    def setup_styles(self):
        """设置ttk样式"""
        style = ttk.Style()

        # 配置Treeview样式 (macOS风格)
        style.configure("Sidebar.Treeview",
                       background=self.colors['sidebar_bg'],
                       foreground=self.colors['text'],
                       fieldbackground=self.colors['sidebar_bg'],
                       borderwidth=0,
                       font=('SF Pro Display', 13))

        style.configure("Notes.Treeview",
                       background=self.colors['white'],
                       foreground=self.colors['text'],
                       fieldbackground=self.colors['white'],
                       borderwidth=0,
                       font=('SF Pro Display', 12))

        # 配置按钮样式
        style.configure("MacOS.TButton",
                       background=self.colors['selected'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('SF Pro Display', 11))

    def create_main_layout(self):
        """创建主布局"""
        # 主容器
        self.main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # 工具栏容器
        self.toolbar_frame = tk.Frame(self.main_frame, bg=self.colors['bg'], height=50)
        self.toolbar_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        self.toolbar_frame.pack_propagate(False)

        # 内容区域容器
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['bg'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 使用PanedWindow创建可调整大小的面板
        self.paned_window = tk.PanedWindow(self.content_frame,
                                          orient=tk.HORIZONTAL,
                                          bg=self.colors['bg'],
                                          sashwidth=1,
                                          sashrelief=tk.FLAT,
                                          borderwidth=0)
        self.paned_window.pack(fill=tk.BOTH, expand=True)

    def create_toolbar(self):
        """创建工具栏"""
        # 左侧按钮组
        left_buttons = tk.Frame(self.toolbar_frame, bg=self.colors['bg'])
        left_buttons.pack(side=tk.LEFT)

        # 添加记事按钮
        self.add_note_btn = tk.Button(left_buttons,
                                     text="+ 新建记事",
                                     bg=self.colors['selected'],
                                     fg='white',
                                     font=('SF Pro Display', 11),
                                     borderwidth=0,
                                     padx=15,
                                     pady=8,
                                     cursor='hand2',
                                     command=self.add_note)
        self.add_note_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 添加分类按钮
        self.add_category_btn = tk.Button(left_buttons,
                                         text="+ 新建分类",
                                         bg=self.colors['border'],
                                         fg=self.colors['text'],
                                         font=('SF Pro Display', 11),
                                         borderwidth=0,
                                         padx=15,
                                         pady=8,
                                         cursor='hand2',
                                         command=self.add_category)
        self.add_category_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧搜索框
        right_frame = tk.Frame(self.toolbar_frame, bg=self.colors['bg'])
        right_frame.pack(side=tk.RIGHT)

        # 搜索框
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(right_frame,
                                    textvariable=self.search_var,
                                    font=('SF Pro Display', 11),
                                    bg=self.colors['white'],
                                    fg=self.colors['text'],
                                    borderwidth=1,
                                    relief=tk.SOLID,
                                    width=25)
        self.search_entry.pack(side=tk.RIGHT, padx=(10, 0))
        self.search_entry.bind('<KeyRelease>', self.on_search)

        # 搜索标签
        search_label = tk.Label(right_frame,
                               text="搜索:",
                               bg=self.colors['bg'],
                               fg=self.colors['text'],
                               font=('SF Pro Display', 11))
        search_label.pack(side=tk.RIGHT)

    def create_sidebar(self):
        """创建左侧分类侧边栏"""
        # 侧边栏容器
        self.sidebar_frame = tk.Frame(self.paned_window,
                                     bg=self.colors['sidebar_bg'],
                                     width=200)
        self.paned_window.add(self.sidebar_frame, minsize=150)

        # 侧边栏标题
        sidebar_title = tk.Label(self.sidebar_frame,
                                text="分类",
                                bg=self.colors['sidebar_bg'],
                                fg=self.colors['text'],
                                font=('SF Pro Display', 14, 'bold'))
        sidebar_title.pack(pady=(15, 10), padx=15, anchor='w')

        # 分类列表
        self.categories_tree = ttk.Treeview(self.sidebar_frame,
                                           style="Sidebar.Treeview",
                                           show='tree',
                                           selectmode='browse')
        self.categories_tree.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        self.categories_tree.bind('<<TreeviewSelect>>', self.on_category_select)
        self.categories_tree.bind('<Button-3>', self.show_category_context_menu)  # 右键菜单

    def create_notes_list(self):
        """创建中间记事列表"""
        # 记事列表容器
        self.notes_frame = tk.Frame(self.paned_window,
                                   bg=self.colors['white'],
                                   width=300)
        self.paned_window.add(self.notes_frame, minsize=250)

        # 记事列表标题栏
        notes_header = tk.Frame(self.notes_frame, bg=self.colors['white'], height=40)
        notes_header.pack(fill=tk.X, padx=15, pady=(15, 0))
        notes_header.pack_propagate(False)

        self.notes_title_label = tk.Label(notes_header,
                                         text="所有记事",
                                         bg=self.colors['white'],
                                         fg=self.colors['text'],
                                         font=('SF Pro Display', 14, 'bold'))
        self.notes_title_label.pack(side=tk.LEFT, anchor='w')

        # 记事数量标签
        self.notes_count_label = tk.Label(notes_header,
                                         text="0 条记事",
                                         bg=self.colors['white'],
                                         fg=self.colors['secondary_text'],
                                         font=('SF Pro Display', 11))
        self.notes_count_label.pack(side=tk.RIGHT, anchor='e')

        # 分隔线
        separator = tk.Frame(self.notes_frame, bg=self.colors['border'], height=1)
        separator.pack(fill=tk.X, padx=15, pady=10)

        # 记事列表
        notes_list_frame = tk.Frame(self.notes_frame, bg=self.colors['white'])
        notes_list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 创建滚动条
        notes_scrollbar = ttk.Scrollbar(notes_list_frame)
        notes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.notes_listbox = tk.Listbox(notes_list_frame,
                                       bg=self.colors['white'],
                                       fg=self.colors['text'],
                                       font=('SF Pro Display', 12),
                                       borderwidth=0,
                                       highlightthickness=0,
                                       selectbackground=self.colors['selected'],
                                       selectforeground='white',
                                       yscrollcommand=notes_scrollbar.set)
        self.notes_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notes_scrollbar.config(command=self.notes_listbox.yview)

        self.notes_listbox.bind('<<ListboxSelect>>', self.on_note_select)
        self.notes_listbox.bind('<Button-3>', self.show_note_context_menu)  # 右键菜单

    def create_editor(self):
        """创建右侧编辑器"""
        # 编辑器容器
        self.editor_frame = tk.Frame(self.paned_window,
                                    bg=self.colors['white'],
                                    width=500)
        self.paned_window.add(self.editor_frame, minsize=400)

        # 编辑器标题栏
        editor_header = tk.Frame(self.editor_frame, bg=self.colors['white'], height=50)
        editor_header.pack(fill=tk.X, padx=20, pady=(20, 0))
        editor_header.pack_propagate(False)

        # 标题输入框
        self.title_var = tk.StringVar()
        self.title_entry = tk.Entry(editor_header,
                                   textvariable=self.title_var,
                                   font=('SF Pro Display', 16, 'bold'),
                                   bg=self.colors['white'],
                                   fg=self.colors['text'],
                                   borderwidth=0,
                                   relief=tk.FLAT)
        self.title_entry.pack(fill=tk.X, pady=(0, 10))
        self.title_entry.bind('<KeyRelease>', self.on_title_change)

        # 元信息栏
        meta_frame = tk.Frame(editor_header, bg=self.colors['white'])
        meta_frame.pack(fill=tk.X)

        self.meta_label = tk.Label(meta_frame,
                                  text="",
                                  bg=self.colors['white'],
                                  fg=self.colors['secondary_text'],
                                  font=('SF Pro Display', 10))
        self.meta_label.pack(side=tk.LEFT)

        # 删除按钮
        self.delete_btn = tk.Button(meta_frame,
                                   text="删除",
                                   bg='#FF3B30',
                                   fg='white',
                                   font=('SF Pro Display', 10),
                                   borderwidth=0,
                                   padx=10,
                                   pady=5,
                                   cursor='hand2',
                                   command=self.delete_note)
        self.delete_btn.pack(side=tk.RIGHT)

        # 分隔线
        separator = tk.Frame(self.editor_frame, bg=self.colors['border'], height=1)
        separator.pack(fill=tk.X, padx=20, pady=10)

        # 内容编辑器
        editor_content_frame = tk.Frame(self.editor_frame, bg=self.colors['white'])
        editor_content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 创建滚动条
        content_scrollbar = ttk.Scrollbar(editor_content_frame)
        content_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.content_text = tk.Text(editor_content_frame,
                                   bg=self.colors['white'],
                                   fg=self.colors['text'],
                                   font=('SF Pro Display', 12),
                                   borderwidth=0,
                                   relief=tk.FLAT,
                                   wrap=tk.WORD,
                                   yscrollcommand=content_scrollbar.set)
        self.content_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        content_scrollbar.config(command=self.content_text.yview)

        self.content_text.bind('<KeyRelease>', self.on_content_change)

        # 初始状态下隐藏编辑器
        self.show_welcome_message()

    def show_welcome_message(self):
        """显示欢迎消息"""
        self.title_entry.config(state='disabled')
        self.content_text.config(state='disabled')
        self.delete_btn.config(state='disabled')

        self.title_var.set("")
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(1.0, "选择一个记事开始编辑，或创建新的记事。")
        self.meta_label.config(text="")

    def load_data(self):
        """加载数据"""
        self.load_categories()
        self.load_notes()

    def load_categories(self):
        """加载分类列表"""
        # 清空现有项目
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)

        cursor = self.conn.cursor()
        cursor.execute('SELECT id, name FROM categories ORDER BY name')
        categories = cursor.fetchall()

        for category_id, name in categories:
            self.categories_tree.insert('', 'end', iid=category_id, text=name, values=(category_id,))

        # 默认选择第一个分类
        if categories:
            self.categories_tree.selection_set(categories[0][0])
            self.current_category_id = categories[0][0]
        else:
            self.current_category_id = None

    def load_notes(self, category_id=None, search_term=""):
        """加载记事列表"""
        self.notes_listbox.delete(0, tk.END)
        self.notes_data = []

        cursor = self.conn.cursor()

        if search_term:
            # 搜索模式
            cursor.execute('''
                SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                FROM notes n
                LEFT JOIN categories c ON n.category_id = c.id
                WHERE n.title LIKE ? OR n.content LIKE ?
                ORDER BY n.updated_at DESC
            ''', (f'%{search_term}%', f'%{search_term}%'))
            self.notes_title_label.config(text=f"搜索结果: {search_term}")
        elif category_id:
            # 按分类筛选
            if category_id == 1:  # "所有记事"分类
                cursor.execute('''
                    SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                    FROM notes n
                    LEFT JOIN categories c ON n.category_id = c.id
                    ORDER BY n.updated_at DESC
                ''')
                self.notes_title_label.config(text="所有记事")
            else:
                cursor.execute('''
                    SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                    FROM notes n
                    LEFT JOIN categories c ON n.category_id = c.id
                    WHERE n.category_id = ?
                    ORDER BY n.updated_at DESC
                ''', (category_id,))
                # 获取分类名称
                cursor.execute('SELECT name FROM categories WHERE id = ?', (category_id,))
                category_name = cursor.fetchone()
                if category_name:
                    self.notes_title_label.config(text=category_name[0])
        else:
            # 默认显示所有记事
            cursor.execute('''
                SELECT n.id, n.title, n.content, n.created_at, n.updated_at, c.name as category_name
                FROM notes n
                LEFT JOIN categories c ON n.category_id = c.id
                ORDER BY n.updated_at DESC
            ''')
            self.notes_title_label.config(text="所有记事")

        notes = cursor.fetchall()

        for note in notes:
            note_id, title, content, created_at, updated_at, category_name = note
            self.notes_data.append({
                'id': note_id,
                'title': title,
                'content': content,
                'created_at': created_at,
                'updated_at': updated_at,
                'category_name': category_name or '未分类'
            })

            # 显示标题，如果标题为空则显示内容的前30个字符
            display_title = title if title.strip() else (content[:30] + "..." if len(content) > 30 else content)
            if not display_title.strip():
                display_title = "无标题"

            self.notes_listbox.insert(tk.END, display_title)

        # 更新记事数量
        count = len(notes)
        self.notes_count_label.config(text=f"{count} 条记事")

        # 如果没有选中的记事，显示欢迎消息
        if not notes:
            self.show_welcome_message()

    # 事件处理方法 (占位符，后续实现具体功能)
    def on_category_select(self, event):
        """分类选择事件"""
        selection = self.categories_tree.selection()
        if selection:
            category_id = int(selection[0])
            self.current_category_id = category_id
            self.load_notes(category_id)

    def on_note_select(self, event):
        """记事选择事件"""
        selection = self.notes_listbox.curselection()
        if selection:
            index = selection[0]
            if index < len(self.notes_data):
                note = self.notes_data[index]
                self.load_note_to_editor(note)

    def on_search(self, event):
        """搜索事件"""
        search_term = self.search_var.get().strip()
        if search_term:
            self.load_notes(search_term=search_term)
        else:
            self.load_notes(self.current_category_id)

    def on_title_change(self, event):
        """标题变化事件"""
        # 占位符 - 后续实现自动保存
        pass

    def on_content_change(self, event):
        """内容变化事件"""
        # 占位符 - 后续实现自动保存
        pass

    def load_note_to_editor(self, note):
        """加载记事到编辑器"""
        self.current_note_id = note['id']

        # 启用编辑器
        self.title_entry.config(state='normal')
        self.content_text.config(state='normal')
        self.delete_btn.config(state='normal')

        # 设置内容
        self.title_var.set(note['title'])
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(1.0, note['content'] or "")

        # 设置元信息
        created_time = datetime.datetime.fromisoformat(note['created_at'].replace('Z', '+00:00'))
        updated_time = datetime.datetime.fromisoformat(note['updated_at'].replace('Z', '+00:00'))

        meta_text = f"创建于 {created_time.strftime('%Y-%m-%d %H:%M')} | "
        meta_text += f"更新于 {updated_time.strftime('%Y-%m-%d %H:%M')} | "
        meta_text += f"分类: {note['category_name']}"

        self.meta_label.config(text=meta_text)

    # 功能方法占位符 (后续实现)
    def add_note(self):
        """添加新记事"""
        messagebox.showinfo("提示", "添加记事功能将在下一步实现")

    def add_category(self):
        """添加新分类"""
        messagebox.showinfo("提示", "添加分类功能将在下一步实现")

    def delete_note(self):
        """删除记事"""
        messagebox.showinfo("提示", "删除记事功能将在下一步实现")

    def show_category_context_menu(self, event):
        """显示分类右键菜单"""
        # 占位符 - 后续实现右键菜单
        pass

    def show_note_context_menu(self, event):
        """显示记事右键菜单"""
        # 占位符 - 后续实现右键菜单
        pass

if __name__ == "__main__":
    app = NotesApp()
    app.root.mainloop()